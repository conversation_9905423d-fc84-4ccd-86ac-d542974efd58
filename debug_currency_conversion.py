#!/usr/bin/env python3
"""
Debug script to help identify the currency conversion percentage issue.
Run this script to test the currency conversion API endpoint directly.
"""

import requests
import json
import sys

def test_currency_conversion_api():
    """Test the currency conversion API endpoint directly."""
    print("🔍 Testing Currency Conversion API Endpoint")
    print("=" * 60)
    
    base_url = "http://localhost:5000"  # Adjust if your app runs on a different port
    
    try:
        # Test the currency conversion endpoint
        print("📡 Testing /api/portfolio/currency endpoint...")
        
        # First, let's check if the server is running
        try:
            response = requests.get(f"{base_url}/", timeout=5)
            print(f"✅ Server is running (status: {response.status_code})")
        except requests.exceptions.RequestException as e:
            print(f"❌ Server not accessible: {e}")
            print("💡 Make sure your Flask app is running with: python app.py")
            return
        
        # Test currency conversion from DKK to THB
        print("\n🔄 Testing currency conversion: DKK → THB")
        conversion_data = {
            "currency": "THB"
        }
        
        response = requests.post(
            f"{base_url}/api/portfolio/currency",
            json=conversion_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Conversion successful!")
            print(f"Response: {json.dumps(result, indent=2)}")
            
            if result.get('success'):
                print(f"✅ Portfolio converted to {result.get('portfolio_currency')}")
                print(f"📊 Converted {result.get('converted_entries')} entries")
            else:
                print(f"❌ Conversion failed: {result.get('error')}")
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")

def debug_portfolio_calculation():
    """Debug the portfolio calculation logic."""
    print("\n🧮 Debug Portfolio Calculation Logic")
    print("=" * 60)
    
    # Simulate portfolio data that might cause the issue
    test_portfolio = [
        {
            'ticker': 'AAPL.US',
            'amount_invested': 10000.0,  # DKK
            'current_value': 8402.0,     # DKK (loss)
            'currency': 'DKK'
        }
    ]
    
    print("📊 Test Portfolio Data:")
    for stock in test_portfolio:
        invested = stock['amount_invested']
        current = stock['current_value']
        gain = current - invested
        percentage = (gain / invested * 100) if invested > 0 else 0
        
        print(f"  {stock['ticker']}:")
        print(f"    Amount Invested: {invested:,.2f} {stock['currency']}")
        print(f"    Current Value: {current:,.2f} {stock['currency']}")
        print(f"    Gain/Loss: {gain:,.2f} {stock['currency']}")
        print(f"    Percentage: {percentage:.2f}%")
    
    # Calculate totals
    total_invested = sum(s['amount_invested'] for s in test_portfolio)
    total_current = sum(s['current_value'] for s in test_portfolio)
    total_gain = total_current - total_invested
    total_percentage = (total_gain / total_invested * 100) if total_invested > 0 else 0
    
    print(f"\n📈 Portfolio Totals:")
    print(f"  Total Invested: {total_invested:,.2f} DKK")
    print(f"  Total Current: {total_current:,.2f} DKK")
    print(f"  Total Gain: {total_gain:,.2f} DKK")
    print(f"  Total Percentage: {total_percentage:.2f}%")
    
    print("\n💡 Expected behavior:")
    print("  When converting to THB, the percentage should remain the same!")
    print("  Only the currency amounts should change, not the percentage.")

def check_browser_cache():
    """Provide instructions for checking browser cache issues."""
    print("\n🌐 Browser Cache Check")
    print("=" * 60)
    print("If the percentage is still wrong after the fix, try:")
    print("1. Hard refresh: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)")
    print("2. Clear browser cache and cookies for localhost")
    print("3. Open in incognito/private browsing mode")
    print("4. Check browser developer tools console for errors")
    print("5. Restart your Flask application completely")

def main():
    """Main debug function."""
    print("🐛 Currency Conversion Debug Tool")
    print("=" * 60)
    print("This tool helps debug the currency conversion percentage issue.")
    print("Make sure your Flask app is running before using this tool.")
    
    # Test the API endpoint
    test_currency_conversion_api()
    
    # Debug calculation logic
    debug_portfolio_calculation()
    
    # Browser cache instructions
    check_browser_cache()
    
    print("\n🔧 DEBUGGING STEPS:")
    print("=" * 60)
    print("1. Run this script while your Flask app is running")
    print("2. Check the API response for any errors")
    print("3. Try the currency conversion in your browser")
    print("4. Check browser developer tools for JavaScript errors")
    print("5. Clear browser cache if percentage still wrong")
    
    print("\n📝 WHAT TO LOOK FOR:")
    print("=" * 60)
    print("• API should return success: true")
    print("• Converted entries count should match your portfolio size")
    print("• Browser should reload the page after conversion")
    print("• Percentage should remain the same after reload")
    
    print("\n🆘 IF STILL NOT WORKING:")
    print("=" * 60)
    print("1. Check Flask app logs for any errors during conversion")
    print("2. Verify that update_portfolio_values() is being called")
    print("3. Check if there are multiple calculate_portfolio_data() functions")
    print("4. Ensure session data is being saved correctly")

if __name__ == "__main__":
    main()
