#!/usr/bin/env python3
"""
Live test script to verify the currency conversion fix works in your actual application.
This script will make real API calls to your running Flask app to test the conversion.
"""

import requests
import json
import time
import sys

def test_live_currency_conversion():
    """Test currency conversion with your live Flask app."""
    print("🧪 Live Currency Conversion Test")
    print("=" * 60)
    print("This script tests the currency conversion fix with your running Flask app.")
    print("Make sure your Flask app is running before continuing.")
    print()
    
    base_url = "http://localhost:5000"  # Adjust if needed
    
    # Test server connectivity
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ Server is running (status: {response.status_code})")
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {e}")
        print("💡 Make sure your Flask app is running with: python app.py")
        return False
    
    # Test 1: Get current portfolio state
    print("\n📊 Step 1: Getting current portfolio state...")
    try:
        # Try to access the portfolio page to get current state
        response = requests.get(f"{base_url}/portfolio", timeout=10)
        if response.status_code == 200:
            print("✅ Portfolio page accessible")
        else:
            print(f"⚠️  Portfolio page returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Error accessing portfolio: {e}")
        return False
    
    # Test 2: Convert from DKK to THB (your exact scenario)
    print("\n🔄 Step 2: Testing DKK → THB conversion...")
    conversion_data = {"currency": "THB"}
    
    try:
        response = requests.post(
            f"{base_url}/api/portfolio/currency",
            json=conversion_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Currency conversion API call successful!")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            
            if result.get('success'):
                print(f"✅ Portfolio converted to {result.get('portfolio_currency')}")
                print(f"📊 Converted {result.get('converted_entries')} entries")
                
                # Wait a moment for the conversion to complete
                print("\n⏳ Waiting 2 seconds for conversion to complete...")
                time.sleep(2)
                
                # Test 3: Check the portfolio page again to see the new percentage
                print("\n📊 Step 3: Checking portfolio after conversion...")
                try:
                    response = requests.get(f"{base_url}/portfolio", timeout=10)
                    if response.status_code == 200:
                        print("✅ Portfolio page loaded after conversion")
                        
                        # Look for percentage in the response
                        if "%" in response.text:
                            print("✅ Found percentage data in response")
                            # Extract percentage patterns (this is a simple check)
                            import re
                            percentages = re.findall(r'[-+]?\d+\.?\d*%', response.text)
                            if percentages:
                                print(f"📈 Found percentages: {percentages[:5]}")  # Show first 5
                        else:
                            print("⚠️  No percentage data found in response")
                    else:
                        print(f"❌ Portfolio page error after conversion: {response.status_code}")
                except Exception as e:
                    print(f"❌ Error checking portfolio after conversion: {e}")
                
            else:
                print(f"❌ Conversion failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        return False
    
    # Test 4: Convert back to DKK to test reverse conversion
    print("\n🔄 Step 4: Testing THB → DKK conversion (reverse test)...")
    reverse_conversion_data = {"currency": "DKK"}
    
    try:
        response = requests.post(
            f"{base_url}/api/portfolio/currency",
            json=reverse_conversion_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Reverse conversion successful: THB → {result.get('portfolio_currency')}")
                print(f"📊 Converted {result.get('converted_entries')} entries back")
            else:
                print(f"❌ Reverse conversion failed: {result.get('error')}")
        else:
            print(f"❌ Reverse conversion API call failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error during reverse conversion: {e}")
    
    print("\n📋 TEST SUMMARY:")
    print("=" * 60)
    print("✅ If the conversion was successful, check your browser:")
    print("   1. Refresh the portfolio page")
    print("   2. Check if the TOTAL GAIN percentage is now consistent")
    print("   3. Try switching currencies again to verify the fix")
    print()
    print("🔍 If the percentage is still wrong:")
    print("   1. Check the Flask app logs for any errors")
    print("   2. Clear your browser cache (Ctrl+F5 or Cmd+Shift+R)")
    print("   3. Try opening the portfolio in an incognito window")
    print("   4. Check browser developer tools console for JavaScript errors")
    
    return True

def main():
    """Main function."""
    print("🚀 Starting live currency conversion test...")
    print("This will test the fix with your actual running Flask application.")
    print()
    
    # Ask user to confirm
    try:
        confirm = input("Press Enter to continue, or Ctrl+C to cancel: ")
    except KeyboardInterrupt:
        print("\n❌ Test cancelled by user.")
        return
    
    success = test_live_currency_conversion()
    
    if success:
        print("\n🎉 Test completed! Check your browser to see if the percentage fix worked.")
    else:
        print("\n❌ Test failed. Check the error messages above.")

if __name__ == "__main__":
    main()
